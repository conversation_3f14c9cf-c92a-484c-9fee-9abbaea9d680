﻿using Application.Common.Specifications;

namespace Application.Interfaces
{
    public interface IGenericRepository<T> where T : class
    {
        // Basic CRUD operations
        IQueryable<T> Query(bool track = false);
        Task<IEnumerable<T>> GetAllAsync();
        Task<T?> GetByIdAsync(object id);
        Task<T?> GetByIdAsync(string id);
        Task AddAsync(T entity);
        Task AddRangeAsync(IList<T> entities);
        void Update(T entity);
        void UpdateRange(IList<T> entities);
        void Delete(T entity);
        void DeleteRange(IList<T> entities);

        // Specification pattern methods
        Task<T?> GetEntityWithSpec(ISpecification<T> spec);
        Task<IReadOnlyList<T>> ListAsync(ISpecification<T> spec);
        Task<int> CountAsync(ISpecification<T> spec);

        // Pagination
        Task<IReadOnlyList<T>> GetPagedAsync(int pageNumber, int pageSize);

        // Existence check
        Task<bool> ExistsAsync(object id);
        Task<bool> AnyAsync(ISpecification<T> spec);
    }
}
