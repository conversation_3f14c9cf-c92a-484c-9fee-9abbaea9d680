using Application.DTOs.Response;
using MediatR;

namespace Application.Features.Products.Commands
{
    public class UpdateProductCommand : IRequest<ProductDto>
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public decimal Price { get; set; }
        public int Stock { get; set; }
        public bool IsActive { get; set; }
        public string? CategoryId { get; set; }
    }
}
