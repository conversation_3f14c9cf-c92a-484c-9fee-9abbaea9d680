using Application.Common.Specifications;
using Application.DTOs.Response;
using Application.Features.Products.Queries;
using Application.Interfaces;
using Domain.Entities;
using MediatR;

namespace Application.Features.Products.Handlers
{
    public class GetProductByIdHandler : IRequestHandler<GetProductByIdQuery, ProductDto?>
    {
        private readonly IUnitOfWork _unitOfWork;

        public GetProductByIdHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<ProductDto?> Handle(GetProductByIdQuery request, CancellationToken cancellationToken)
        {
            var spec = new ProductsWithCategorySpecification();
            var products = await _unitOfWork.Products.ListAsync(spec);
            var product = products.FirstOrDefault(p => p.Id == request.Id);

            if (product == null)
                return null;

            return new ProductDto
            {
                Id = product.Id,
                Name = product.Name,
                Description = product.Description,
                Price = product.Price,
                Stock = product.Stock,
                IsActive = product.IsActive,
                CategoryId = product.CategoryId,
                Category = product.Category != null ? new CategoryDto
                {
                    Id = product.Category.Id,
                    Name = product.Category.Name,
                    Description = product.Category.Description,
                    IsActive = product.Category.IsActive,
                    CreatedBy = product.Category.CreatedBy,
                    CreatedTime = product.Category.CreatedTime,
                    LastUpdatedBy = product.Category.LastUpdatedBy,
                    LastUpdatedTime = product.Category.LastUpdatedTime
                } : null,
                CreatedBy = product.CreatedBy,
                CreatedTime = product.CreatedTime,
                LastUpdatedBy = product.LastUpdatedBy,
                LastUpdatedTime = product.LastUpdatedTime
            };
        }
    }
}
