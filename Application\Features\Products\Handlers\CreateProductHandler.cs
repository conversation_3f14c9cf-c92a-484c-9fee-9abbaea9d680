using Application.DTOs.Response;
using Application.Features.Products.Commands;
using Application.Interfaces;
using Domain.Common.Events;
using Domain.Entities;
using MediatR;

namespace Application.Features.Products.Handlers
{
    public class CreateProductHandler : IRequestHandler<CreateProductCommand, ProductDto>
    {
        private readonly IUnitOfWork _unitOfWork;

        public CreateProductHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<ProductDto> Handle(CreateProductCommand request, CancellationToken cancellationToken)
        {
            // Validate category exists if provided
            if (!string.IsNullOrEmpty(request.CategoryId))
            {
                var categoryExists = await _unitOfWork.Categories.ExistsAsync(request.CategoryId);
                if (!categoryExists)
                {
                    throw new ArgumentException($"Category with ID {request.CategoryId} does not exist.");
                }
            }

            // Create new product
            var product = new Product
            {
                Name = request.Name,
                Description = request.Description,
                Price = request.Price,
                Stock = request.Stock,
                IsActive = request.IsActive,
                CategoryId = request.CategoryId
            };

            // Add domain event
            product.AddDomainEvent(new ProductCreatedEvent(product.Id, product.Name, product.Price));

            // Save to repository
            await _unitOfWork.Products.AddAsync(product);
            await _unitOfWork.SaveAsync(cancellationToken);

            // Get the created product with category
            var createdProduct = await _unitOfWork.Products.GetByIdAsync(product.Id);
            
            // Map to DTO
            return new ProductDto
            {
                Id = createdProduct!.Id,
                Name = createdProduct.Name,
                Description = createdProduct.Description,
                Price = createdProduct.Price,
                Stock = createdProduct.Stock,
                IsActive = createdProduct.IsActive,
                CategoryId = createdProduct.CategoryId,
                CreatedBy = createdProduct.CreatedBy,
                CreatedTime = createdProduct.CreatedTime,
                LastUpdatedBy = createdProduct.LastUpdatedBy,
                LastUpdatedTime = createdProduct.LastUpdatedTime
            };
        }
    }
}
