using Domain.Entities;

namespace Application.Common.Specifications
{
    public class ProductsWithCategorySpecification : BaseSpecification<Product>
    {
        public ProductsWithCategorySpecification() : base(null)
        {
            AddInclude(x => x.Category!);
            ApplyOrderBy(x => x.Name);
        }

        public ProductsWithCategorySpecification(string categoryId) 
            : base(x => x.CategoryId == categoryId)
        {
            AddInclude(x => x.Category!);
            ApplyOrderBy(x => x.Name);
        }
    }

    public class ActiveProductsSpecification : BaseSpecification<Product>
    {
        public ActiveProductsSpecification() : base(x => x.IsActive)
        {
            AddInclude(x => x.Category!);
            ApplyOrderBy(x => x.Name);
        }
    }

    public class ProductByNameSpecification : BaseSpecification<Product>
    {
        public ProductByNameSpecification(string name) 
            : base(x => x.Name.ToLower().Contains(name.ToLower()))
        {
            AddInclude(x => x.Category!);
        }
    }

    public class ProductsPaginatedSpecification : BaseSpecification<Product>
    {
        public ProductsPaginatedSpecification(int skip, int take) : base(null)
        {
            AddInclude(x => x.Category!);
            ApplyOrderBy(x => x.Name);
            ApplyPaging(skip, take);
        }
    }
}
